2025-08-27 08:56:54,644 - WARNING -  * <PERSON>bu<PERSON> is active!
2025-08-27 08:56:54,649 - INFO -  * Debugger PIN: 423-099-573
2025-08-27 08:56:56,547 - INFO -  * Detected change in 'g:\\代码处理工具\\闲鱼商品采集 - 重构\\WEB版本\\测试.py', reloading
2025-08-27 08:56:59,316 - WARNING -  * <PERSON>bu<PERSON> is active!
2025-08-27 08:56:59,322 - INFO -  * Debugger PIN: 423-099-573
2025-08-27 08:57:43,817 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-27 08:57:43,817 - INFO - [33mPress CTRL+C to quit[0m
2025-08-27 08:57:43,818 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-27 08:57:45,129 - WARNING -  * <PERSON>bu<PERSON> is active!
2025-08-27 08:57:45,135 - INFO -  * Debugger PIN: 423-099-573
2025-08-27 08:57:46,869 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:46] "GET / HTTP/1.1" 200 -
2025-08-27 08:57:47,063 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:47] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 08:57:47,069 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:47] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 08:57:47,683 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:47] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-27 08:57:47,684 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:47] "GET /socket.io/?EIO=4&transport=polling&t=PZf1-sK HTTP/1.1" 200 -
2025-08-27 08:57:47,685 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:47] "GET /api/采集状态 HTTP/1.1" 200 -
2025-08-27 08:57:47,742 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:47] "POST /socket.io/?EIO=4&transport=polling&t=PZf1-tN&sid=kLM6pA5CnkhYhYwqAAAA HTTP/1.1" 200 -
2025-08-27 08:57:47,744 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:47] "GET /socket.io/?EIO=4&transport=polling&t=PZf1-tO&sid=kLM6pA5CnkhYhYwqAAAA HTTP/1.1" 200 -
2025-08-27 08:57:47,796 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:47] "GET /socket.io/?EIO=4&transport=polling&t=PZf1-uI&sid=kLM6pA5CnkhYhYwqAAAA HTTP/1.1" 200 -
2025-08-27 08:57:47,811 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:47] "GET /socket.io/?EIO=4&transport=polling&t=PZf1-uT&sid=kLM6pA5CnkhYhYwqAAAA HTTP/1.1" 200 -
2025-08-27 08:57:48,896 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:48] "GET /数据采集 HTTP/1.1" 200 -
2025-08-27 08:57:48,941 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:48] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 08:57:48,943 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:48] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 08:57:49,075 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_CD HTTP/1.1" 200 -
2025-08-27 08:57:49,076 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_CE HTTP/1.1" 200 -
2025-08-27 08:57:49,079 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-27 08:57:49,140 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "POST /socket.io/?EIO=4&transport=polling&t=PZf1_DE&sid=E5SFlIU1U4jGBBV8AAAC HTTP/1.1" 200 -
2025-08-27 08:57:49,140 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_DF&sid=E5SFlIU1U4jGBBV8AAAC HTTP/1.1" 200 -
2025-08-27 08:57:49,141 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "POST /socket.io/?EIO=4&transport=polling&t=PZf1_DF.0&sid=f8rO8BigQSxw0NyTAAAD HTTP/1.1" 200 -
2025-08-27 08:57:49,141 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "GET /socket.io/?EIO=4&transport=websocket&sid=kLM6pA5CnkhYhYwqAAAA HTTP/1.1" 200 -
2025-08-27 08:57:49,142 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_DF.1&sid=f8rO8BigQSxw0NyTAAAD HTTP/1.1" 200 -
2025-08-27 08:57:49,152 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_DT&sid=E5SFlIU1U4jGBBV8AAAC HTTP/1.1" 200 -
2025-08-27 08:57:49,157 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_DV&sid=f8rO8BigQSxw0NyTAAAD HTTP/1.1" 200 -
2025-08-27 08:57:49,175 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_Dq&sid=E5SFlIU1U4jGBBV8AAAC HTTP/1.1" 200 -
2025-08-27 08:57:49,180 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_Ds&sid=f8rO8BigQSxw0NyTAAAD HTTP/1.1" 200 -
2025-08-27 08:57:50,311 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "GET /数据采集 HTTP/1.1" 200 -
2025-08-27 08:57:50,318 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "GET /socket.io/?EIO=4&transport=websocket&sid=E5SFlIU1U4jGBBV8AAAC HTTP/1.1" 200 -
2025-08-27 08:57:50,318 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "GET /socket.io/?EIO=4&transport=websocket&sid=f8rO8BigQSxw0NyTAAAD HTTP/1.1" 200 -
2025-08-27 08:57:50,344 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 08:57:50,347 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 08:57:50,467 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_Xy HTTP/1.1" 200 -
2025-08-27 08:57:50,468 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-27 08:57:50,475 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_Xz HTTP/1.1" 200 -
2025-08-27 08:57:50,504 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "POST /socket.io/?EIO=4&transport=polling&t=PZf1_YZ&sid=c5O2B3Fg6ld0sXKFAAAG HTTP/1.1" 200 -
2025-08-27 08:57:50,505 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_Ya&sid=c5O2B3Fg6ld0sXKFAAAG HTTP/1.1" 200 -
2025-08-27 08:57:50,513 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "POST /socket.io/?EIO=4&transport=polling&t=PZf1_Yb&sid=8QNxA4DsC9LM3G4aAAAH HTTP/1.1" 200 -
2025-08-27 08:57:50,513 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_Yb.0&sid=8QNxA4DsC9LM3G4aAAAH HTTP/1.1" 200 -
2025-08-27 08:57:50,529 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_Y_&sid=8QNxA4DsC9LM3G4aAAAH HTTP/1.1" 200 -
2025-08-27 08:57:50,541 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_ZA&sid=8QNxA4DsC9LM3G4aAAAH HTTP/1.1" 200 -
2025-08-27 08:57:50,747 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "GET /数据查看 HTTP/1.1" 200 -
2025-08-27 08:57:50,797 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 08:57:50,798 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 08:57:51,027 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:51] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_gk HTTP/1.1" 200 -
2025-08-27 08:57:51,054 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:51] "GET /api/获取商品数据 HTTP/1.1" 200 -
2025-08-27 08:57:51,064 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:51] "POST /socket.io/?EIO=4&transport=polling&t=PZf1_hL&sid=KBfu-E8SkQguB168AAAK HTTP/1.1" 200 -
2025-08-27 08:57:51,065 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:51] "GET /socket.io/?EIO=4&transport=websocket&sid=8QNxA4DsC9LM3G4aAAAH HTTP/1.1" 200 -
2025-08-27 08:57:51,066 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:51] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_hL.0&sid=KBfu-E8SkQguB168AAAK HTTP/1.1" 200 -
2025-08-27 08:57:51,066 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:51] "GET /socket.io/?EIO=4&transport=websocket&sid=c5O2B3Fg6ld0sXKFAAAG HTTP/1.1" 200 -
2025-08-27 08:57:51,124 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:51] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_iF&sid=KBfu-E8SkQguB168AAAK HTTP/1.1" 200 -
2025-08-27 09:02:00,468 - INFO -  * Detected change in 'g:\\代码处理工具\\闲鱼商品采集 - 重构\\WEB版本\\主程序.py', reloading
2025-08-27 09:02:00,469 - INFO -  * Detected change in 'g:\\代码处理工具\\闲鱼商品采集 - 重构\\WEB版本\\主程序.py', reloading
2025-08-27 09:02:01,465 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-27 09:02:02,637 - WARNING -  * Debugger is active!
2025-08-27 09:02:02,640 - INFO -  * Debugger PIN: 423-099-573
2025-08-27 09:02:03,222 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:03] "GET /socket.io/?EIO=4&transport=polling&t=PZf2zFJ HTTP/1.1" 200 -
2025-08-27 09:02:03,227 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:03] "POST /socket.io/?EIO=4&transport=polling&t=PZf2zFO&sid=BCWHcSZOUUOhuzKtAAAA HTTP/1.1" 200 -
2025-08-27 09:02:03,228 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:03] "GET /socket.io/?EIO=4&transport=polling&t=PZf2zFO.0&sid=BCWHcSZOUUOhuzKtAAAA HTTP/1.1" 200 -
2025-08-27 09:02:22,701 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-27 09:02:22,701 - INFO - [33mPress CTRL+C to quit[0m
2025-08-27 09:02:22,702 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-27 09:02:23,888 - WARNING -  * Debugger is active!
2025-08-27 09:02:23,893 - INFO -  * Debugger PIN: 423-099-573
2025-08-27 09:02:50,874 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:50] "GET /数据查看 HTTP/1.1" 200 -
2025-08-27 09:02:51,039 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:51] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 09:02:51,053 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:51] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 09:02:51,579 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:51] "GET /socket.io/?EIO=4&transport=polling&t=PZf392v HTTP/1.1" 200 -
2025-08-27 09:02:51,582 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:51] "GET /api/获取商品数据 HTTP/1.1" 200 -
2025-08-27 09:02:51,619 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:51] "POST /socket.io/?EIO=4&transport=polling&t=PZf393V&sid=rWVvJp8KIvJhvriJAAAC HTTP/1.1" 200 -
2025-08-27 09:02:51,620 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:51] "GET /socket.io/?EIO=4&transport=polling&t=PZf393W&sid=rWVvJp8KIvJhvriJAAAC HTTP/1.1" 200 -
2025-08-27 09:02:51,888 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:51] "GET /socket.io/?EIO=4&transport=polling&t=PZf3971&sid=rWVvJp8KIvJhvriJAAAC HTTP/1.1" 200 -
2025-08-27 09:02:52,666 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:52] "GET /socket.io/?EIO=4&transport=websocket&sid=BCWHcSZOUUOhuzKtAAAA HTTP/1.1" 200 -
2025-08-27 09:04:09,807 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:09] "GET /新上架商品 HTTP/1.1" 200 -
2025-08-27 09:04:09,836 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:09] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 09:04:09,848 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:09] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 09:04:10,433 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:10] "GET /socket.io/?EIO=4&transport=polling&t=PZf3SIz HTTP/1.1" 200 -
2025-08-27 09:04:10,439 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:10] "GET /api/获取新上架商品 HTTP/1.1" 200 -
2025-08-27 09:04:10,488 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:10] "POST /socket.io/?EIO=4&transport=polling&t=PZf3SJr&sid=-G-kczaCy84mOUUkAAAE HTTP/1.1" 200 -
2025-08-27 09:04:10,489 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:10] "GET /socket.io/?EIO=4&transport=polling&t=PZf3SJr.0&sid=-G-kczaCy84mOUUkAAAE HTTP/1.1" 200 -
2025-08-27 09:04:10,766 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:10] "GET /socket.io/?EIO=4&transport=polling&t=PZf3SNE&sid=-G-kczaCy84mOUUkAAAE HTTP/1.1" 200 -
2025-08-27 09:04:23,620 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-27 09:04:23,620 - INFO - [33mPress CTRL+C to quit[0m
2025-08-27 09:04:23,620 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-27 09:04:24,912 - WARNING -  * Debugger is active!
2025-08-27 09:04:24,916 - INFO -  * Debugger PIN: 423-099-573
