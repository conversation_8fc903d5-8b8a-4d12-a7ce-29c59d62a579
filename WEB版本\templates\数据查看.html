{% extends "基础模板.html" %}

{% block title %}数据查看 - 闲鱼商品采集工具{% endblock %}

{% block content %}
<div class="row">
  <!-- 筛选和搜索区域 -->
  <div class="col-12 mb-4">
    <div class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">
          <i class="fas fa-filter me-2"></i>
          数据筛选
        </h5>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-3 mb-3">
            <label for="seller-filter" class="form-label">选择卖家</label>
            <select class="form-select" id="seller-filter" onchange="筛选数据()">
              <option value="">全部卖家</option>
              <!-- 卖家选项将通过JavaScript动态加载 -->
            </select>
          </div>
          <div class="col-md-3 mb-3">
            <label for="search-input" class="form-label">搜索商品</label>
            <input type="text" class="form-control" id="search-input" placeholder="输入商品标题或ID" onkeyup="搜索商品(this.value)">
          </div>
          <div class="col-md-2 mb-3">
            <label for="want-count-filter" class="form-label">想要人数</label>
            <select class="form-select" id="want-count-filter" onchange="筛选数据()">
              <option value="">全部</option>
              <option value="0">0人想要</option>
              <option value="1-10">1-10人</option>
              <option value="11-50">11-50人</option>
              <option value="51+">50人以上</option>
            </select>
          </div>
          <div class="col-md-2 mb-3">
            <label for="sort-by" class="form-label">排序方式</label>
            <select class="form-select" id="sort-by" onchange="排序数据()">
              <option value="time-desc">最新采集</option>
              <option value="time-asc">最早采集</option>
              <option value="want-desc">想要人数↓</option>
              <option value="want-asc">想要人数↑</option>
              <option value="title-asc">标题A-Z</option>
            </select>
          </div>
          <div class="col-md-2 mb-3">
            <label class="form-label">&nbsp;</label>
            <div class="d-grid">
              <button type="button" class="btn btn-outline-secondary" onclick="重置筛选()">
                <i class="fas fa-undo me-1"></i>重置
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 数据统计区域 -->
  <div class="col-12 mb-4">
    <div class="row">
      <div class="col-md-3">
        <div class="card text-center">
          <div class="card-body">
            <h3 class="text-primary" id="total-sellers">-</h3>
            <p class="text-muted mb-0">总卖家数</p>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card text-center">
          <div class="card-body">
            <h3 class="text-success" id="total-products">-</h3>
            <p class="text-muted mb-0">总商品数</p>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card text-center">
          <div class="card-body">
            <h3 class="text-info" id="filtered-products">-</h3>
            <p class="text-muted mb-0">筛选结果</p>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card text-center">
          <div class="card-body">
            <h3 class="text-warning" id="wanted-products">-</h3>
            <p class="text-muted mb-0">有想要商品</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 数据表格区域 -->
  <div class="col-12">
    <div class="card">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
          <i class="fas fa-table me-2"></i>
          商品数据
        </h5>
        <div>
          <button type="button" class="btn btn-sm btn-outline-primary me-2" onclick="导出数据()">
            <i class="fas fa-download me-1"></i>导出数据
          </button>
          <button type="button" class="btn btn-sm btn-outline-success" onclick="刷新数据()">
            <i class="fas fa-refresh me-1"></i>刷新数据
          </button>
        </div>
      </div>
      <div class="card-body">
        <!-- 加载状态 -->
        <div id="loading-indicator" class="text-center py-5" style="display: none;">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
          </div>
          <p class="mt-2 text-muted">正在加载数据...</p>
        </div>

        <!-- 数据表格 -->
        <div class="table-responsive" id="data-table-container">
          <table class="table table-hover" id="data-table">
            <thead class="table-light">
              <tr>
                <th>
                  <input type="checkbox" id="select-all-products" onchange="全选商品(this.checked)">
                </th>
                <th>商品标题</th>
                <th>商品ID</th>
                <th>卖家</th>
                <th>想要人数</th>
                <th>游戏名称</th>
                <th>首次采集</th>
                <th>最近采集</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody id="data-table-body">
              <!-- 数据将通过JavaScript动态加载 -->
            </tbody>
          </table>
        </div>

        <!-- 分页 -->
        <nav aria-label="数据分页" id="pagination-container" style="display: none;">
          <ul class="pagination justify-content-center" id="pagination">
            <!-- 分页将通过JavaScript动态生成 -->
          </ul>
        </nav>

        <!-- 空数据提示 -->
        <div id="empty-data" class="text-center py-5" style="display: none;">
          <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
          <h5 class="text-muted">暂无数据</h5>
          <p class="text-muted">请先进行数据采集，或调整筛选条件</p>
          <a href="{{ url_for('数据采集页面') }}" class="btn btn-primary">
            <i class="fas fa-download me-1"></i>开始采集
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 商品详情模态框 -->
<div class="modal fade" id="product-detail-modal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">商品详情</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body" id="product-detail-content">
        <!-- 详情内容将通过JavaScript动态加载 -->
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
        <button type="button" class="btn btn-primary" onclick="复制商品链接()">
          <i class="fas fa-copy me-1"></i>复制链接
        </button>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let allData = [];
let filteredData = [];
let selectedProducts = new Set();
let currentPage = 1;
let itemsPerPage = 50;
let currentProductId = null;

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
  加载数据();
  初始化事件监听();
});

function 初始化事件监听() {
  // 监听状态更新
  document.addEventListener('statusUpdate', function(event) {
    const data = event.detail;
    if (data.message.includes('采集完成') || data.message.includes('保存')) {
      // 采集完成后自动刷新数据
      setTimeout(() => {
        刷新数据();
      }, 1000);
    }
  });
}

function 加载数据() {
  显示加载状态(true);

  // 调用API获取真实数据
  fetch('/api/获取商品数据')
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        allData = data.data;
        filteredData = [...allData];
        更新统计信息();
        显示数据();
        加载卖家选项();
      } else {
        显示错误消息('加载数据失败: ' + data.error);
        allData = [];
        filteredData = [];
        更新统计信息();
      }
      显示加载状态(false);
    })
    .catch(error => {
      console.error('加载数据失败:', error);
      显示错误消息('网络错误，无法加载数据');
      allData = [];
      filteredData = [];
      更新统计信息();
      显示加载状态(false);
    });
}

function 显示加载状态(loading) {
  const loadingIndicator = document.getElementById('loading-indicator');
  const tableContainer = document.getElementById('data-table-container');
  const emptyData = document.getElementById('empty-data');
  
  if (loading) {
    loadingIndicator.style.display = 'block';
    tableContainer.style.display = 'none';
    emptyData.style.display = 'none';
  } else {
    loadingIndicator.style.display = 'none';
    if (filteredData.length > 0) {
      tableContainer.style.display = 'block';
      emptyData.style.display = 'none';
    } else {
      tableContainer.style.display = 'none';
      emptyData.style.display = 'block';
    }
  }
}

function 更新统计信息() {
  const totalSellers = new Set(allData.map(item => item.卖家ID)).size;
  const totalProducts = allData.length;
  const filteredProducts = filteredData.length;
  const wantedProducts = allData.filter(item => item.想要人数 > 0).length;
  
  document.getElementById('total-sellers').textContent = totalSellers;
  document.getElementById('total-products').textContent = totalProducts;
  document.getElementById('filtered-products').textContent = filteredProducts;
  document.getElementById('wanted-products').textContent = wantedProducts;
}

function 显示数据() {
  const tbody = document.getElementById('data-table-body');
  tbody.innerHTML = '';
  
  // 分页处理
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const pageData = filteredData.slice(startIndex, endIndex);
  
  pageData.forEach(item => {
    const row = document.createElement('tr');
    row.innerHTML = `
      <td>
        <input type="checkbox" class="product-checkbox" value="${item.商品ID}" 
               onchange="选择商品('${item.商品ID}', this.checked)">
      </td>
      <td>
        <div class="text-truncate" style="max-width: 300px;" title="${item.商品标题}">
          ${item.商品标题}
        </div>
      </td>
      <td>
        <code>${item.商品ID}</code>
      </td>
      <td>${item.卖家名称}</td>
      <td>
        <span class="badge ${item.想要人数 > 0 ? 'bg-success' : 'bg-secondary'}">
          ${item.想要人数}人
        </span>
      </td>
      <td>${item.游戏名称 || '-'}</td>
      <td>
        <small class="text-muted">${item.首次采集时间}</small>
      </td>
      <td>
        <small class="text-muted">${item.最近采集时间}</small>
      </td>
      <td>
        <button class="btn btn-sm btn-outline-primary me-1" onclick="查看详情('${item.商品ID}')" title="查看详情">
          <i class="fas fa-eye"></i>
        </button>
        <button class="btn btn-sm btn-outline-success" onclick="复制商品ID('${item.商品ID}')" title="复制ID">
          <i class="fas fa-copy"></i>
        </button>
      </td>
    `;
    tbody.appendChild(row);
  });
  
  更新分页();
}

function 加载卖家选项() {
  const sellerFilter = document.getElementById('seller-filter');

  // 使用Map来去重卖家，以卖家ID为key
  const sellersMap = new Map();
  allData.forEach(item => {
    const sellerId = item.卖家ID;
    const sellerName = item.卖家名称;
    if (sellerId && !sellersMap.has(sellerId)) {
      sellersMap.set(sellerId, sellerName);
    }
  });

  // 清空现有选项（保留"全部卖家"）
  sellerFilter.innerHTML = '<option value="">全部卖家</option>';

  // 将Map转换为数组并排序
  const sellers = Array.from(sellersMap.entries()).sort((a, b) => a[1].localeCompare(b[1]));

  sellers.forEach(([sellerId, sellerName]) => {
    const option = document.createElement('option');
    option.value = sellerId;
    option.textContent = sellerName;
    sellerFilter.appendChild(option);
  });
}

function 筛选数据() {
  const sellerFilter = document.getElementById('seller-filter').value;
  const wantCountFilter = document.getElementById('want-count-filter').value;
  
  filteredData = allData.filter(item => {
    // 卖家筛选
    if (sellerFilter && item.卖家ID !== sellerFilter) {
      return false;
    }
    
    // 想要人数筛选
    if (wantCountFilter) {
      const wantCount = item.想要人数;
      switch (wantCountFilter) {
        case '0':
          if (wantCount !== 0) return false;
          break;
        case '1-10':
          if (wantCount < 1 || wantCount > 10) return false;
          break;
        case '11-50':
          if (wantCount < 11 || wantCount > 50) return false;
          break;
        case '51+':
          if (wantCount <= 50) return false;
          break;
      }
    }
    
    return true;
  });
  
  currentPage = 1;
  更新统计信息();
  显示数据();
  显示加载状态(false);
}

function 搜索商品(keyword) {
  if (!keyword.trim()) {
    筛选数据();
    return;
  }
  
  const lowerKeyword = keyword.toLowerCase();
  filteredData = allData.filter(item => {
    return item.商品标题.toLowerCase().includes(lowerKeyword) ||
           item.商品ID.includes(keyword) ||
           (item.游戏名称 && item.游戏名称.toLowerCase().includes(lowerKeyword));
  });
  
  currentPage = 1;
  更新统计信息();
  显示数据();
}

function 排序数据() {
  const sortBy = document.getElementById('sort-by').value;
  
  filteredData.sort((a, b) => {
    switch (sortBy) {
      case 'time-desc':
        return new Date(b.最近采集时间) - new Date(a.最近采集时间);
      case 'time-asc':
        return new Date(a.最近采集时间) - new Date(b.最近采集时间);
      case 'want-desc':
        return b.想要人数 - a.想要人数;
      case 'want-asc':
        return a.想要人数 - b.想要人数;
      case 'title-asc':
        return a.商品标题.localeCompare(b.商品标题);
      default:
        return 0;
    }
  });
  
  显示数据();
}

function 重置筛选() {
  document.getElementById('seller-filter').value = '';
  document.getElementById('search-input').value = '';
  document.getElementById('want-count-filter').value = '';
  document.getElementById('sort-by').value = 'time-desc';
  
  filteredData = [...allData];
  currentPage = 1;
  更新统计信息();
  显示数据();
}

function 选择商品(productId, checked) {
  if (checked) {
    selectedProducts.add(productId);
  } else {
    selectedProducts.delete(productId);
  }
  
  // 更新全选状态
  const allCheckboxes = document.querySelectorAll('.product-checkbox');
  const checkedCheckboxes = document.querySelectorAll('.product-checkbox:checked');
  const selectAllCheckbox = document.getElementById('select-all-products');
  
  if (checkedCheckboxes.length === 0) {
    selectAllCheckbox.indeterminate = false;
    selectAllCheckbox.checked = false;
  } else if (checkedCheckboxes.length === allCheckboxes.length) {
    selectAllCheckbox.indeterminate = false;
    selectAllCheckbox.checked = true;
  } else {
    selectAllCheckbox.indeterminate = true;
  }
}

function 全选商品(checked) {
  const checkboxes = document.querySelectorAll('.product-checkbox');
  selectedProducts.clear();
  
  checkboxes.forEach(checkbox => {
    checkbox.checked = checked;
    if (checked) {
      selectedProducts.add(checkbox.value);
    }
  });
}

function 查看详情(productId) {
  const product = allData.find(item => item.商品ID === productId);
  if (!product) return;
  
  currentProductId = productId;
  
  const content = document.getElementById('product-detail-content');
  content.innerHTML = `
    <div class="row">
      <div class="col-md-6">
        <h6>基本信息</h6>
        <table class="table table-sm">
          <tr><td>商品ID</td><td><code>${product.商品ID}</code></td></tr>
          <tr><td>商品标题</td><td>${product.商品标题}</td></tr>
          <tr><td>卖家</td><td>${product.卖家名称} (${product.卖家ID})</td></tr>
          <tr><td>想要人数</td><td><span class="badge bg-success">${product.想要人数}人</span></td></tr>
          <tr><td>游戏名称</td><td>${product.游戏名称 || '-'}</td></tr>
        </table>
      </div>
      <div class="col-md-6">
        <h6>采集信息</h6>
        <table class="table table-sm">
          <tr><td>首次采集</td><td>${product.首次采集时间}</td></tr>
          <tr><td>最近采集</td><td>${product.最近采集时间}</td></tr>
        </table>
      </div>
    </div>
  `;
  
  const modal = new bootstrap.Modal(document.getElementById('product-detail-modal'));
  modal.show();
}

function 复制商品ID(productId) {
  复制到剪贴板(productId);
}

function 复制商品链接() {
  if (currentProductId) {
    const link = `https://www.goofish.com/item?id=${currentProductId}`;
    复制到剪贴板(link);
  }
}

function 导出数据() {
  if (filteredData.length === 0) {
    显示通知('没有数据可导出', 'warning');
    return;
  }
  
  // 转换为CSV格式
  const headers = ['商品ID', '商品标题', '卖家名称', '卖家ID', '想要人数', '游戏名称', '首次采集时间', '最近采集时间'];
  const csvContent = [
    headers.join(','),
    ...filteredData.map(item => [
      item.商品ID,
      `"${item.商品标题.replace(/"/g, '""')}"`,
      item.卖家名称,
      item.卖家ID,
      item.想要人数,
      item.游戏名称 || '',
      item.首次采集时间,
      item.最近采集时间
    ].join(','))
  ].join('\n');
  
  const filename = `商品数据_${new Date().toISOString().slice(0, 10)}.csv`;
  下载文件('\ufeff' + csvContent, filename, 'text/csv;charset=utf-8');
  显示通知('数据导出成功', 'success');
}

function 刷新数据() {
  加载数据();
}

function 更新分页() {
  const totalPages = Math.ceil(filteredData.length / itemsPerPage);
  const paginationContainer = document.getElementById('pagination-container');
  const pagination = document.getElementById('pagination');
  
  if (totalPages <= 1) {
    paginationContainer.style.display = 'none';
    return;
  }
  
  paginationContainer.style.display = 'block';
  pagination.innerHTML = '';
  
  // 上一页
  const prevLi = document.createElement('li');
  prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
  prevLi.innerHTML = `<a class="page-link" href="#" onclick="跳转页面(${currentPage - 1})">上一页</a>`;
  pagination.appendChild(prevLi);
  
  // 页码
  const startPage = Math.max(1, currentPage - 2);
  const endPage = Math.min(totalPages, currentPage + 2);
  
  for (let i = startPage; i <= endPage; i++) {
    const li = document.createElement('li');
    li.className = `page-item ${i === currentPage ? 'active' : ''}`;
    li.innerHTML = `<a class="page-link" href="#" onclick="跳转页面(${i})">${i}</a>`;
    pagination.appendChild(li);
  }
  
  // 下一页
  const nextLi = document.createElement('li');
  nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
  nextLi.innerHTML = `<a class="page-link" href="#" onclick="跳转页面(${currentPage + 1})">下一页</a>`;
  pagination.appendChild(nextLi);
}

function 跳转页面(page) {
  const totalPages = Math.ceil(filteredData.length / itemsPerPage);
  if (page < 1 || page > totalPages) return;
  
  currentPage = page;
  显示数据();
}
</script>
{% endblock %}
